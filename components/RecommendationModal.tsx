'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { createSupabaseClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';

type Writer = {
  id: string;
  name: string | null;
  avatar: string | null;
  profile_picture_url: string | null;
};

type Creator = Writer & {
  is_recommended: boolean;
};

type RecommendationModalProps = {
  onClose: () => void;
};

export default function RecommendationModal({ onClose }: RecommendationModalProps) {
  const [creators, setCreators] = useState<Creator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createSupabaseClient();

  useEffect(() => {
    const fetchCreators = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setIsLoading(false);
          return;
        }

        // Get all users who have written diary entries (active creators)
        const { data: creators, error: creatorsError } = await supabase
          .from('users')
          .select('id, name, avatar, profile_picture_url, entry_count')
          .neq('id', user.id) // Exclude current user
          .not('name', 'is', null) // Only users with names
          .gt('entry_count', 0) // Only users with content
          .order('entry_count', { ascending: false })
          .limit(50);

        console.log('Creators query result:', { creators, creatorsError });

        // Get current recommendations
        const { data: recommendations } = await supabase
          .from('favorite_creators')
          .select('writer_id')
          .eq('user_id', user.id);

        const recommendedIds = new Set(recommendations?.map(r => r.writer_id) || []);

        // Add recommendation status
        const creatorsWithStatus = (creators || []).map((creator: any) => ({
          id: creator.id,
          name: creator.name,
          avatar: creator.avatar,
          profile_picture_url: creator.profile_picture_url,
          is_recommended: recommendedIds.has(creator.id)
        }));

        setCreators(creatorsWithStatus);
      } catch (error) {
        console.error('Error fetching creators:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCreators();
  }, [supabase]);

  const handleToggleRecommendation = async (creator: Creator) => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    if (creator.is_recommended) {
      await supabase
        .from('favorite_creators')
        .delete()
        .match({ user_id: user.id, writer_id: creator.id });
    } else {
      await supabase
        .from('favorite_creators')
        .insert({ user_id: user.id, writer_id: creator.id });
    }

    setCreators(creators.map(c => 
      c.id === creator.id ? { ...c, is_recommended: !c.is_recommended } : c
    ));
    
    // Refresh the page after a short delay to show updated recommendations
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-4 sm:p-6 w-full max-w-lg max-h-[90vh] sm:max-h-[80vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">Manage Recommendations</h3>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : creators.length > 0 ? (
          <div className="space-y-2">
            {creators.map((creator) => (
              <div key={creator.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg gap-2">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  {(creator.profile_picture_url || creator.avatar) ? (
                    <Image
                      src={creator.profile_picture_url || creator.avatar || ''}
                      alt={creator.name || 'Creator'}
                      width={32}
                      height={32}
                      className="rounded-full object-cover flex-shrink-0"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                      <span className="text-xs font-semibold text-gray-600">
                        {creator.name?.[0]?.toUpperCase() || '?'}
                      </span>
                    </div>
                  )}
                  <span className="font-medium text-sm truncate">{creator.name}</span>
                </div>
                <Button
                  size="sm"
                  variant={creator.is_recommended ? "outline" : "default"}
                  className={`flex-shrink-0 text-xs px-2 py-1 ${creator.is_recommended ? "text-red-600 hover:text-red-700 hover:bg-red-50" : "bg-green-600 hover:bg-green-700"}`}
                  onClick={() => handleToggleRecommendation(creator)}
                >
                  {creator.is_recommended ? 'Remove' : 'Add'}
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-2">No creators found.</p>
            <p className="text-gray-400">Check back later for more creators to recommend!</p>
          </div>
        )}

        <div className="flex gap-2 mt-6">
          <Button 
            onClick={onClose}
            variant="outline"
            className="flex-1"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
