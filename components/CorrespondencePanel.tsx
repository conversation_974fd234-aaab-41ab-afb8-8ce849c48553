'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'

interface Correspondence {
  id: string
  type: 'moment' | 'letter' | 'note'
  title: string
  body: string
  data?: any
  read_at?: string
  created_at: string
  sender?: {
    id: string
    name: string
    avatar?: string
  }
}

interface CorrespondencePanelProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  onUnreadCountChange?: (count: number) => void
}

export function CorrespondencePanel({ isOpen, onClose, userId, onUnreadCountChange }: CorrespondencePanelProps) {
  const [activeTab, setActiveTab] = useState<'moments' | 'letters' | 'notes'>('moments')
  const [correspondence, setCorrespondence] = useState<Correspondence[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen && userId) {
      fetchCorrespondence()
    }
  }, [isOpen, userId, activeTab])

  const fetchCorrespondence = async () => {
    setLoading(true)
    try {
      // Fetch notifications (moments & notes)
      const { data: notifications } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      // TODO: Fetch direct messages (letters) when we implement that table
      
      const formattedCorrespondence: Correspondence[] = (notifications || []).map(notif => ({
        id: notif.id,
        type: notif.type === 'recommendation' ? 'moment' : 'note',
        title: notif.title,
        body: notif.body,
        data: notif.data,
        read_at: notif.read_at,
        created_at: notif.created_at,
        sender: notif.data?.recommender_name ? {
          id: notif.data.recommender_id,
          name: notif.data.recommender_name,
        } : undefined
      }))

      setCorrespondence(formattedCorrespondence)
    } catch (error) {
      console.error('Error fetching correspondence:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (correspondenceId: string) => {
    try {
      await supabase
        .from('notifications')
        .update({ read_at: new Date().toISOString() })
        .eq('id', correspondenceId)

      // Update local state
      setCorrespondence(prev =>
        prev.map(item =>
          item.id === correspondenceId
            ? { ...item, read_at: new Date().toISOString() }
            : item
        )
      )

      // Notify parent component to update unread count
      if (onUnreadCountChange) {
        const newUnreadCount = correspondence.filter(item =>
          !item.read_at && item.id !== correspondenceId
        ).length
        onUnreadCountChange(newUnreadCount)
      }
    } catch (error) {
      console.error('Error marking as read:', error)
    }
  }

  const filteredCorrespondence = correspondence.filter(item => {
    if (activeTab === 'moments') return item.type === 'moment'
    if (activeTab === 'letters') return item.type === 'letter'
    if (activeTab === 'notes') return item.type === 'note'
    return true
  })

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  const handleNotificationClick = (item: Correspondence) => {
    // Mark as read first
    if (!item.read_at) {
      markAsRead(item.id)
    }

    // Navigate based on notification type
    if (item.type === 'moment' && item.data?.recommender_id) {
      // For recommendations, go to the recommender's profile
      window.location.href = `/u/${item.data.recommender_id}`
    }
    // Add more navigation logic for other types as needed

    // Close the panel
    onClose()
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />
      
      {/* Panel */}
      <div className="fixed top-16 right-4 w-96 max-w-[calc(100vw-2rem)] bg-white rounded-2xl shadow-2xl border border-gray-100 z-50 max-h-[80vh] flex flex-col">
        
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-serif text-gray-800">Correspondence</h2>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ×
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-1 bg-gray-50 rounded-lg p-1">
            {[
              { key: 'moments', label: 'Moments', icon: '✨' },
              { key: 'letters', label: 'Letters', icon: '💌' },
              { key: 'notes', label: 'Notes', icon: '📝' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.key
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
            </div>
          ) : filteredCorrespondence.length > 0 ? (
            <div className="p-2">
              {filteredCorrespondence.map((item) => (
                <div
                  key={item.id}
                  className={`p-4 m-2 rounded-xl border transition-all hover:shadow-sm cursor-pointer ${
                    item.read_at
                      ? 'bg-gray-50 border-gray-100 hover:bg-gray-100'
                      : 'bg-blue-50 border-blue-100 hover:bg-blue-100'
                  }`}
                  onClick={() => handleNotificationClick(item)}
                >
                  <div className="flex items-start gap-3">
                    {/* Avatar or Icon */}
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center flex-shrink-0">
                      {item.sender?.avatar ? (
                        <img 
                          src={item.sender.avatar} 
                          alt={item.sender.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-lg">
                          {item.type === 'moment' ? '✨' : item.type === 'letter' ? '💌' : '📝'}
                        </span>
                      )}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-800 text-sm mb-1">
                        {item.title}
                      </h4>
                      <p className="text-gray-600 text-sm leading-relaxed mb-2">
                        {item.body}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(item.created_at)}
                        </span>
                        {!item.read_at && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-2xl">
                  {activeTab === 'moments' ? '✨' : activeTab === 'letters' ? '💌' : '📝'}
                </span>
              </div>
              <h3 className="font-medium text-gray-800 mb-2">
                No {activeTab} yet
              </h3>
              <p className="text-gray-500 text-sm">
                {activeTab === 'moments' && "Story moments will appear here"}
                {activeTab === 'letters' && "Direct messages will appear here"}
                {activeTab === 'notes' && "System notes will appear here"}
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
