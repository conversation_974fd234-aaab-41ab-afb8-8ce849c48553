'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { MailingListButton } from "@/components/MailingListButton"
import { PaywallContent } from "@/components/PaywallContent"
import { StoryMap } from "@/components/StoryMap"
import { VideoThumbnail } from "@/components/VideoThumbnail"
import { createSupabaseClient } from "@/lib/supabase/client"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
  view_count?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title: string; view_count: number; custom_thumbnail_url?: string }>
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
}

interface UnifiedProfileClientProps {
  user: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

export function UnifiedProfileClient({
  user,
  diaryEntries,
  projects,
  hasActiveSubscription,
  isFollowing,
  isOwnProfile
}: UnifiedProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'diary' | 'books'>('diary')
  const [loading, setLoading] = useState(false)
  const [recommendedCreators, setRecommendedCreators] = useState<any[]>([])
  const supabase = createSupabaseClient()

  // Check if user has monetization set up
  const hasMonetizationSetup = user?.price_monthly && user?.stripe_account_id

  // Fetch recommended creators
  useEffect(() => {
    const fetchRecommendedCreators = async () => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, name, bio, profile_picture_url, avatar, subscriber_count, entry_count')
          .neq('id', user.id) // Exclude current user
          .gt('entry_count', 0) // Only users with content
          .order('subscriber_count', { ascending: false })
          .limit(4)

        if (data && !error) {
          setRecommendedCreators(data)
        }
      } catch (error) {
        console.error('Error fetching recommended creators:', error)
      }
    }

    if (user?.id) {
      fetchRecommendedCreators()
    }
  }, [user?.id, supabase])

  // Safety checks
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">😕</div>
          <h2 className="text-xl font-serif text-gray-800 mb-2">Profile not found</h2>
          <p className="text-gray-600">This user doesn't exist or their profile is private.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Profile Header */}
        <div className="bg-white rounded-xl shadow-sm p-8 mb-6 relative">
          {/* Edit Profile Button - Top Right for Own Profile */}
          {isOwnProfile && (
            <Link
              href="/profile/edit"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-sm font-medium transition-colors flex items-center gap-1"
            >
              ⚙️ Edit
            </Link>
          )}

          <div className="flex flex-col lg:flex-row gap-8">

            {/* Avatar */}
            <div className="flex-shrink-0 mx-auto lg:mx-0">
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg">
                {user.profile_picture_url || user.avatar ? (
                  <img
                    src={user.profile_picture_url || user.avatar}
                    alt={user.name || 'Profile'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <span className="text-4xl font-serif text-gray-600">
                    {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                  </span>
                )}
              </div>
            </div>

            {/* Profile Info & Actions */}
            <div className="flex-1 text-center lg:text-left">

              {/* Name & Bio */}
              <div className="mb-6">
                <h1 className="text-3xl font-serif text-gray-800 mb-3">
                  {user.name || user.email || 'Anonymous User'}
                </h1>
                {user.bio && (
                  <p className="text-gray-600 text-lg leading-relaxed max-w-2xl">
                    {user.bio}
                  </p>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-4 gap-3 sm:gap-6 mb-6 text-center">
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{user.follower_count || 0}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Followers</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{diaryEntries.length}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Diary Entries</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{projects.length}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Book Projects</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">
                    {diaryEntries.reduce((total, entry) => {
                      // Rough word count estimation: body_md length / 5
                      return total + Math.floor((entry.body_md?.length || 0) / 5)
                    }, 0).toLocaleString()}
                  </div>
                  <div className="text-xs sm:text-sm text-gray-500">Total Words</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {isOwnProfile ? (
                  // Own Profile Actions
                  <>
                    {/* Primary Create Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <Link
                        href="/write/diary"
                        className="bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                      >
                        ✍️ Write Entry
                      </Link>
                      <Link
                        href="/publishing"
                        className="bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors text-center"
                      >
                        📚 Publish Book
                      </Link>
                    </div>
                  </>
                ) : (
                  // Visitor Actions - Follow is primary
                  <>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <FollowButton
                        writerId={user.id}
                        writerName={user.name}
                        initialIsFollowing={isFollowing}
                      />

                      <MailingListButton
                        creatorId={user.id}
                        creatorName={user.name}
                        customUrl={user.custom_url}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <div className="flex">
              <button
                onClick={() => setActiveTab('diary')}
                className={`flex-1 py-4 px-4 sm:px-6 text-center font-medium transition-colors ${
                  activeTab === 'diary'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="block sm:inline">📖</span>
                <span className="block sm:inline sm:ml-2">Diary Entries ({diaryEntries.length})</span>
              </button>
              <button
                onClick={() => setActiveTab('books')}
                className={`flex-1 py-4 px-4 sm:px-6 text-center font-medium transition-colors ${
                  activeTab === 'books'
                    ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="block sm:inline">📚</span>
                <span className="block sm:inline sm:ml-2">Books ({projects.length})</span>
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'diary' && (
              <DiaryEntriesSection
                entries={diaryEntries}
                hasActiveSubscription={hasActiveSubscription}
                writerName={user.name}
                writerId={user.id}
                isOwnProfile={isOwnProfile}
              />
            )}

            {activeTab === 'books' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects.length > 0 ? (
                  projects.map((project) => (
                    <Link key={project.id} href={`/books/${project.id}`}>
                      <div className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                        {project.cover_image_url && (
                          <img
                            src={project.cover_image_url}
                            alt={project.title}
                            className="w-full h-48 object-cover rounded-lg mb-4"
                          />
                        )}
                        <h3 className="font-serif text-lg text-gray-800 mb-2">{project.title}</h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.description}</p>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-500 capitalize">{project.genre}</span>
                          <span className="font-medium text-gray-800">
                            {project.price_amount === 0 ? 'Free' : formatPrice(project.price_amount)}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <div className="text-4xl mb-4">📚</div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No books published yet</h3>
                    <p className="text-gray-600">
                      {isOwnProfile ? "Ready to publish your first book?" : "Check back later for new books."}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Recommended Creators Section */}
        {recommendedCreators.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm p-6 mt-6">
            <h3 className="text-lg font-serif text-gray-800 mb-4">Recommended Creators</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {recommendedCreators.map((creator) => (
                <div key={creator.id} className="bg-gray-50 rounded-lg p-4 text-center">
                  {/* Avatar */}
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center mx-auto mb-3 overflow-hidden">
                    {creator.profile_picture_url || creator.avatar ? (
                      <img
                        src={creator.profile_picture_url || creator.avatar}
                        alt={creator.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-600">
                        {creator.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  {/* Name */}
                  <Link href={`/u/${creator.id}`}>
                    <h4 className="font-medium text-gray-800 mb-2 truncate hover:text-blue-600 transition-colors">
                      {creator.name}
                    </h4>
                  </Link>

                  {/* Bio - 5 words max */}
                  <p className="text-xs text-gray-600 text-center mb-3 h-8 flex items-center justify-center">
                    {creator.bio ?
                      `${creator.bio.split(' ').slice(0, 5).join(' ')}${creator.bio.split(' ').length > 5 ? '...' : ''}`
                      : 'No bio'
                    }
                  </p>

                  {/* Follow Button */}
                  <FollowButton
                    writerId={creator.id}
                    writerName={creator.name}
                    initialIsFollowing={false}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Diary Entries Section Component - Uniform Grid Layout
function DiaryEntriesSection({
  entries,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile
}: {
  entries: DiaryEntry[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
}) {
  if (entries.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📔</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Diary Entries Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries yet. Check back soon!
        </p>
      </div>
    )
  }

  // Separate pinned and regular entries
  const pinnedEntries = entries.filter((entry: any) => entry.is_pinned === true)
  const regularEntries = entries.filter((entry: any) => entry.is_pinned !== true)

  return (
    <div className="space-y-8">
      {/* Pinned Entry - Featured */}
      {pinnedEntries.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <span className="text-yellow-500">📌</span>
            Start Here
          </h3>
          <DiaryEntryCard
            entry={pinnedEntries[0]}
            hasAccess={pinnedEntries[0].is_free || hasActiveSubscription || isOwnProfile}
            featured={true}
          />
        </div>
      )}

      {/* Regular Entries Grid - Compact & Uniform */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
        {regularEntries.map((entry) => (
          <DiaryEntryCard
            key={entry.id}
            entry={entry}
            hasAccess={entry.is_free || hasActiveSubscription || isOwnProfile}
            featured={false}
          />
        ))}
      </div>
    </div>
  )
}

// Individual Diary Entry Card Component
function DiaryEntryCard({
  entry,
  hasAccess,
  featured = false
}: {
  entry: DiaryEntry
  hasAccess: boolean
  featured?: boolean
}) {
  // Get preview text (first 120 characters)
  const previewText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .substring(0, 120)
    .trim()

  const wordCount = entry.body_md.split(' ').length
  const readTime = Math.ceil(wordCount / 200)
  const firstPhoto = entry.photos?.[0]
  const firstVideo = entry.videos?.[0]
  const totalViews = (entry.view_count || 0) + (entry.videos?.reduce((sum, video) => sum + (video.view_count || 0), 0) || 0)

  return (
    <Link href={`/d/${entry.id}`} className="group block h-full">
      <div className={`bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col ${
        featured ? 'sm:col-span-2 lg:col-span-3' : ''
      }`}>

        {/* Image/Video Section - Square Aspect Ratio */}
        <div className="relative bg-gradient-to-br from-purple-100 to-blue-100 aspect-square">
          {firstVideo ? (
            <VideoThumbnail
              videoUrl={firstVideo.r2_public_url}
              customThumbnailUrl={firstVideo.custom_thumbnail_url}
              alt={entry.title}
              className="w-full h-full"
              timeInSeconds={1}
              showPlayButton={true}
              playButtonSize="md"
            />
          ) : firstPhoto ? (
            <img
              src={firstPhoto.url}
              alt={firstPhoto.alt_text || entry.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="opacity-50 text-xl">📝</span>
            </div>
          )}

          {/* Status Badges */}
          <div className="absolute top-1.5 right-1.5 flex gap-1">
            {entry.is_free && (
              <span className="bg-green-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                FREE
              </span>
            )}
            {(entry as any).is_pinned === true && (
              <span className="bg-yellow-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                📌
              </span>
            )}
          </div>

          {/* Lock Overlay for Paid Content */}
          {!hasAccess && (
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
              <div className="bg-white/90 rounded-full p-2">
                <svg className="w-4 h-4 text-gray-700" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          )}
        </div>

        {/* Content Section - Compact */}
        <div className={`p-3 ${featured ? 'sm:p-4' : ''} flex-1 flex flex-col`}>
          {/* Card Header */}
          <div className="flex items-start justify-between mb-1.5">
            <h3 className={`font-serif text-gray-800 line-clamp-1 group-hover:text-purple-600 transition-colors ${
              featured ? 'text-lg sm:text-xl md:text-2xl' : 'text-sm font-semibold'
            }`} title={entry.title}>
              {entry.title}
            </h3>
            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
              {formatDate(entry.created_at).split(',')[0]}
            </span>
          </div>

          {/* Card Description - Compact */}
          <p className={`text-gray-600 line-clamp-2 mb-auto ${
            featured ? 'text-sm' : 'text-xs'
          }`}>
            {previewText}
            {entry.body_md.length > 120 && '...'}
          </p>

          {/* Stats and Button - Compact Bottom Section */}
          <div className="mt-2">
            {/* Stats Row - Clean and Spacious */}
            <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
              <span>{wordCount > 1000 ? `${Math.round(wordCount/1000)}k` : wordCount} words</span>

              {/* View Count */}
              {totalViews > 0 && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                    </svg>
                    <span>{formatViewCount(totalViews)}</span>
                  </span>
                </>
              )}
            </div>

            {/* Read Button - Compact */}
            <button className={`w-full bg-purple-600 text-white py-2 rounded-md font-medium hover:bg-purple-700 transition-colors text-xs ${
              featured ? 'text-sm py-2.5' : ''
            }`}>
              Read Story
            </button>
          </div>
        </div>
      </div>
    </Link>
  )
}
