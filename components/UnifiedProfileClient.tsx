'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { MailingListButton } from "@/components/MailingListButton"
import { PaywallContent } from "@/components/PaywallContent"
import { StoryMap } from "@/components/StoryMap"
import { createSupabaseClient } from "@/lib/supabase/client"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
  view_count?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title: string; view_count: number; custom_thumbnail_url?: string }>
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
}

interface UnifiedProfileClientProps {
  user: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function UnifiedProfileClient({
  user,
  diaryEntries,
  projects,
  hasActiveSubscription,
  isFollowing,
  isOwnProfile
}: UnifiedProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'diary' | 'books'>('diary')
  const [loading, setLoading] = useState(false)
  const [recommendedCreators, setRecommendedCreators] = useState<any[]>([])
  const supabase = createSupabaseClient()

  // Check if user has monetization set up
  const hasMonetizationSetup = user?.price_monthly && user?.stripe_account_id

  // Fetch recommended creators
  useEffect(() => {
    const fetchRecommendedCreators = async () => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, name, bio, profile_picture_url, avatar, subscriber_count, entry_count')
          .neq('id', user.id) // Exclude current user
          .gt('entry_count', 0) // Only users with content
          .order('subscriber_count', { ascending: false })
          .limit(4)

        if (data && !error) {
          setRecommendedCreators(data)
        }
      } catch (error) {
        console.error('Error fetching recommended creators:', error)
      }
    }

    if (user?.id) {
      fetchRecommendedCreators()
    }
  }, [user?.id, supabase])

  // Safety checks
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">😕</div>
          <h2 className="text-xl font-serif text-gray-800 mb-2">Profile not found</h2>
          <p className="text-gray-600">This user doesn't exist or their profile is private.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Profile Header */}
        <div className="bg-white rounded-xl shadow-sm p-8 mb-6 relative">
          {/* Edit Profile Button - Top Right for Own Profile */}
          {isOwnProfile && (
            <Link
              href="/profile/edit"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-sm font-medium transition-colors flex items-center gap-1"
            >
              ⚙️ Edit
            </Link>
          )}

          <div className="flex flex-col lg:flex-row gap-8">

            {/* Avatar */}
            <div className="flex-shrink-0 mx-auto lg:mx-0">
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg">
                {user.profile_picture_url || user.avatar ? (
                  <img
                    src={user.profile_picture_url || user.avatar}
                    alt={user.name || 'Profile'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <span className="text-4xl font-serif text-gray-600">
                    {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                  </span>
                )}
              </div>
            </div>

            {/* Profile Info & Actions */}
            <div className="flex-1 text-center lg:text-left">

              {/* Name & Bio */}
              <div className="mb-6">
                <h1 className="text-3xl font-serif text-gray-800 mb-3">
                  {user.name || user.email || 'Anonymous User'}
                </h1>
                {user.bio && (
                  <p className="text-gray-600 text-lg leading-relaxed max-w-2xl">
                    {user.bio}
                  </p>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-4 gap-3 sm:gap-6 mb-6 text-center">
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{user.follower_count || 0}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Followers</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{diaryEntries.length}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Diary Entries</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">{projects.length}</div>
                  <div className="text-xs sm:text-sm text-gray-500">Book Projects</div>
                </div>
                <div>
                  <div className="text-lg sm:text-2xl font-bold text-gray-800">
                    {diaryEntries.reduce((total, entry) => {
                      // Rough word count estimation: body_md length / 5
                      return total + Math.floor((entry.body_md?.length || 0) / 5)
                    }, 0).toLocaleString()}
                  </div>
                  <div className="text-xs sm:text-sm text-gray-500">Total Words</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {isOwnProfile ? (
                  // Own Profile Actions
                  <>
                    {/* Primary Create Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <Link
                        href="/write/diary"
                        className="bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                      >
                        ✍️ Write Entry
                      </Link>
                      <Link
                        href="/publishing"
                        className="bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors text-center"
                      >
                        📚 Publish Book
                      </Link>
                    </div>
                  </>
                ) : (
                  // Visitor Actions - Follow is primary
                  <>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <FollowButton
                        writerId={user.id}
                        writerName={user.name}
                        initialIsFollowing={isFollowing}
                      />

                      <MailingListButton
                        creatorId={user.id}
                        creatorName={user.name}
                        customUrl={user.custom_url}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <div className="flex">
              <button
                onClick={() => setActiveTab('diary')}
                className={`flex-1 py-4 px-4 sm:px-6 text-center font-medium transition-colors ${
                  activeTab === 'diary'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="block sm:inline">📖</span>
                <span className="block sm:inline sm:ml-2">Diary Entries ({diaryEntries.length})</span>
              </button>
              <button
                onClick={() => setActiveTab('books')}
                className={`flex-1 py-4 px-4 sm:px-6 text-center font-medium transition-colors ${
                  activeTab === 'books'
                    ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="block sm:inline">📚</span>
                <span className="block sm:inline sm:ml-2">Books ({projects.length})</span>
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'diary' && (
              <div className="space-y-6">
                {diaryEntries.length > 0 ? (
                  diaryEntries.map((entry) => (
                    <div key={entry.id} className="border-b border-gray-100 pb-6 last:border-b-0">
                      <div className="flex justify-between items-start mb-3">
                        <Link href={`/d/${entry.id}`} className="flex-1">
                          <h3 className="text-lg font-serif text-gray-800 hover:text-blue-600 transition-colors">
                            {entry.title}
                          </h3>
                        </Link>
                        <div className="flex items-center gap-2 text-sm text-gray-500 ml-4">
                          {entry.is_free && (
                            <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                              FREE
                            </span>
                          )}
                          <span>{formatDate(entry.created_at)}</span>
                        </div>
                      </div>
                      
                      <PaywallContent
                        content={entry.body_md}
                        isOwner={isOwnProfile}
                        hasAccess={entry.is_free || hasActiveSubscription || isOwnProfile}
                        showPaywall={!entry.is_free && !hasActiveSubscription && !isOwnProfile}
                        writerId={user.id}
                        writerName={user.name}
                        priceMonthly={user.price_monthly}
                      />
                      
                      {entry.photos && entry.photos.length > 0 && (
                        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-2">
                          {entry.photos.map((photo) => (
                            <img
                              key={photo.id}
                              src={photo.url}
                              alt={photo.alt_text || 'Photo'}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">📖</div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No diary entries yet</h3>
                    <p className="text-gray-600">
                      {isOwnProfile ? "Start sharing your story!" : "Check back later for new entries."}
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'books' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects.length > 0 ? (
                  projects.map((project) => (
                    <Link key={project.id} href={`/books/${project.id}`}>
                      <div className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                        {project.cover_image_url && (
                          <img
                            src={project.cover_image_url}
                            alt={project.title}
                            className="w-full h-48 object-cover rounded-lg mb-4"
                          />
                        )}
                        <h3 className="font-serif text-lg text-gray-800 mb-2">{project.title}</h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.description}</p>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-500 capitalize">{project.genre}</span>
                          <span className="font-medium text-gray-800">
                            {project.price_amount === 0 ? 'Free' : formatPrice(project.price_amount)}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <div className="text-4xl mb-4">📚</div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No books published yet</h3>
                    <p className="text-gray-600">
                      {isOwnProfile ? "Ready to publish your first book?" : "Check back later for new books."}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Recommended Creators Section */}
        {recommendedCreators.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm p-6 mt-6">
            <h3 className="text-lg font-serif text-gray-800 mb-4">Recommended Creators</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {recommendedCreators.map((creator) => (
                <div key={creator.id} className="bg-gray-50 rounded-lg p-4 text-center">
                  {/* Avatar */}
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center mx-auto mb-3 overflow-hidden">
                    {creator.profile_picture_url || creator.avatar ? (
                      <img
                        src={creator.profile_picture_url || creator.avatar}
                        alt={creator.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-600">
                        {creator.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  {/* Name */}
                  <Link href={`/u/${creator.id}`}>
                    <h4 className="font-medium text-gray-800 mb-2 truncate hover:text-blue-600 transition-colors">
                      {creator.name}
                    </h4>
                  </Link>

                  {/* Bio - 5 words max */}
                  <p className="text-xs text-gray-600 text-center mb-3 h-8 flex items-center justify-center">
                    {creator.bio ?
                      `${creator.bio.split(' ').slice(0, 5).join(' ')}${creator.bio.split(' ').length > 5 ? '...' : ''}`
                      : 'No bio'
                    }
                  </p>

                  {/* Follow Button */}
                  <FollowButton
                    writerId={creator.id}
                    writerName={creator.name}
                    initialIsFollowing={false}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
