'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Star, TrendingUp, BookOpen, DollarSign, Award, Crown, Medal, Trophy, Download, Eye, Heart } from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
}

interface Category {
  genre: string
  book_count: number
}

interface EnhancedBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function EnhancedBestsellerChart({ limit = 10, showHeader = true }: EnhancedBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })

      if (error) {
        console.error('Bestsellers query error:', error)
        throw error
      }

      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })

      if (error) {
        console.error('Categories query error:', error)
        return
      }

      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return { icon: Crown, color: 'text-yellow-500', bg: 'bg-yellow-50', border: 'border-yellow-200' }
    if (rank === 2) return { icon: Award, color: 'text-gray-400', bg: 'bg-gray-50', border: 'border-gray-200' }
    if (rank === 3) return { icon: Medal, color: 'text-amber-600', bg: 'bg-amber-50', border: 'border-amber-200' }
    return { icon: Trophy, color: 'text-blue-500', bg: 'bg-blue-50', border: 'border-blue-200' }
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { text: '#1 Best Seller', color: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white' }
    if (rank === 2) return { text: '#2 Best Seller', color: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white' }
    if (rank === 3) return { text: '#3 Best Seller', color: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white' }
    if (rank <= 10) return { text: `#${rank} Best Seller`, color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' }
    if (rank <= 100) return { text: `Top 100`, color: 'bg-gradient-to-r from-green-500 to-green-600 text-white' }
    return { text: `#${rank}`, color: 'bg-gray-100 text-gray-600' }
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      {showHeader && (
        <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 border-b border-gray-200">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-amber-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-amber-700" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">OnlyDiary Bestsellers</h1>
              <p className="text-gray-600 text-sm">Discover the most popular books from independent authors</p>
            </div>
          </div>

          {/* Enhanced Free/Paid Tabs */}
          <div className="flex space-x-2 bg-white rounded-xl p-2 shadow-sm border border-gray-200 mb-6">
            <button
              onClick={() => setActiveTab('free')}
              className={`flex-1 flex items-center justify-center gap-3 py-3 px-6 rounded-lg text-sm font-semibold transition-all ${
                activeTab === 'free'
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-md transform scale-[1.02]'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <BookOpen className="w-5 h-5" />
              <div className="text-left">
                <div>Free Books</div>
                <div className="text-xs opacity-80">Download at no cost</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('paid')}
              className={`flex-1 flex items-center justify-center gap-3 py-3 px-6 rounded-lg text-sm font-semibold transition-all ${
                activeTab === 'paid'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md transform scale-[1.02]'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <DollarSign className="w-5 h-5" />
              <div className="text-left">
                <div>Premium Books</div>
                <div className="text-xs opacity-80">Support authors directly</div>
              </div>
            </button>
          </div>

          {/* Enhanced Category Filter */}
          {categories.length > 0 && (
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                Browse by Genre:
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-sm bg-white shadow-sm"
              >
                <option value="all">All Genres</option>
                {categories.map((category) => (
                  <option key={category.genre} value={category.genre}>
                    {category.genre} ({category.book_count} books)
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      <div className="p-6 bg-gray-50">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-amber-200 border-t-amber-600"></div>
              <p className="text-gray-600 font-medium">Loading bestsellers...</p>
            </div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="space-y-3">
            {bestsellers.map((book, index) => {
              const rankIcon = getRankIcon(book.rank)
              const rankBadge = getRankBadge(book.rank)
              const RankIconComponent = rankIcon.icon

              return (
                <Link
                  key={book.id}
                  href={`/books/${book.id}`}
                  className="block bg-white rounded-xl border border-gray-200 hover:border-amber-300 hover:shadow-lg transition-all duration-300 group overflow-hidden"
                >
                  <div className="flex items-center gap-6 p-6">
                    {/* Rank Section */}
                    <div className="flex-shrink-0 flex flex-col items-center gap-2">
                      <div className={`w-12 h-12 rounded-full ${rankIcon.bg} ${rankIcon.border} border-2 flex items-center justify-center`}>
                        <RankIconComponent className={`w-6 h-6 ${rankIcon.color}`} />
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-bold ${rankBadge.color}`}>
                        {rankBadge.text}
                      </div>
                    </div>

                    {/* Book Cover */}
                    <div className="flex-shrink-0 w-20 h-28 bg-gray-100 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow">
                      {book.cover_image_url ? (
                        <img
                          src={book.cover_image_url}
                          alt={book.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-amber-100 via-orange-100 to-red-100 flex items-center justify-center">
                          <BookOpen className="w-8 h-8 text-amber-600" />
                        </div>
                      )}
                    </div>

                    {/* Book Details */}
                    <div className="flex-1 min-w-0 space-y-2">
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 group-hover:text-amber-700 transition-colors line-clamp-2 leading-tight">
                          {book.title}
                        </h3>
                        <p className="text-gray-600 font-medium">
                          by {book.author_name}
                        </p>
                      </div>

                      {book.genre && (
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            {book.genre}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Download className="w-4 h-4" />
                          <span className="font-medium">{book.sales_count.toLocaleString()}</span>
                          <span>downloads</span>
                        </div>
                        {book.is_daily_data && (
                          <div className="flex items-center gap-1 text-amber-600">
                            <TrendingUp className="w-4 h-4" />
                            <span className="text-xs font-medium">Daily Chart</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Price & Action */}
                    <div className="flex-shrink-0 text-right space-y-3">
                      <div className="text-2xl font-bold text-gray-900">
                        {formatPrice(book.price_amount)}
                      </div>
                      <div className={`px-4 py-2 rounded-lg font-semibold text-sm transition-colors ${
                        book.price_amount && book.price_amount > 0
                          ? 'bg-blue-600 text-white group-hover:bg-blue-700'
                          : 'bg-green-600 text-white group-hover:bg-green-700'
                      }`}>
                        {book.price_amount && book.price_amount > 0 ? 'Buy Now' : 'Download Free'}
                      </div>
                      <div className="text-xs text-gray-500">
                        Rank #{book.rank}
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-xl border-2 border-dashed border-gray-200">
            <div className="max-w-md mx-auto">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <BookOpen className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">
                No {activeTab} bestsellers yet
              </h3>
              <p className="text-gray-600 mb-6">
                {selectedCategory === 'all'
                  ? `Be the first to publish a ${activeTab} book and claim the #1 spot!`
                  : `No ${activeTab} books found in ${selectedCategory}. Try a different genre or be the first to publish in this category!`
                }
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <TrendingUp className="w-4 h-4" />
                <span>Rankings update daily at 8AM</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
