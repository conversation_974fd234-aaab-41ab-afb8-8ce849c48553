'use client'

import { useState, useEffect, useRef } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import {
  Star, TrendingUp, BookOpen, DollarSign, Award, Crown, Medal, Trophy,
  Download, Eye, Heart, Play, Pause, Volume2, VolumeX, Share2,
  MessageCircle, Bookmark, Users, Zap, Fire, Sparkles, ChevronLeft,
  ChevronRight, MoreHorizontal, Clock, Calendar, Globe
} from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
  // Social metrics (would come from database)
  likes_count?: number
  comments_count?: number
  shares_count?: number
  reading_time?: number
  trending_score?: number
  viral_velocity?: number
}

interface Category {
  genre: string
  book_count: number
}

interface EnhancedBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function EnhancedBestsellerChart({ limit = 10, showHeader = true }: EnhancedBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })

      if (error) {
        console.error('Bestsellers query error:', error)
        throw error
      }

      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })

      if (error) {
        console.error('Categories query error:', error)
        return
      }

      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return { icon: Crown, color: 'text-yellow-500', bg: 'bg-yellow-50', border: 'border-yellow-200' }
    if (rank === 2) return { icon: Award, color: 'text-gray-400', bg: 'bg-gray-50', border: 'border-gray-200' }
    if (rank === 3) return { icon: Medal, color: 'text-amber-600', bg: 'bg-amber-50', border: 'border-amber-200' }
    return { icon: Trophy, color: 'text-blue-500', bg: 'bg-blue-50', border: 'border-blue-200' }
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { text: '#1 Best Seller', color: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white' }
    if (rank === 2) return { text: '#2 Best Seller', color: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white' }
    if (rank === 3) return { text: '#3 Best Seller', color: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white' }
    if (rank <= 10) return { text: `#${rank} Best Seller`, color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' }
    if (rank <= 100) return { text: `Top 100`, color: 'bg-gradient-to-r from-green-500 to-green-600 text-white' }
    return { text: `#${rank}`, color: 'bg-gray-100 text-gray-600' }
  }

  return (
    <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl shadow-2xl border border-purple-500/20 overflow-hidden backdrop-blur-sm">
      {showHeader && (
        <div className="relative bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-orange-600/10 p-8 border-b border-purple-500/20">
          {/* Animated background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 animate-pulse"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500"></div>

          <div className="relative z-10">
            <div className="flex items-center gap-4 mb-8">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur-lg opacity-50 animate-pulse"></div>
                <div className="relative p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                  <Fire className="w-8 h-8 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                  Global Bestsellers
                </h1>
                <p className="text-purple-200 text-lg font-medium">
                  The world's most downloaded independent books
                </p>
              </div>
              <div className="ml-auto flex items-center gap-2 px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white text-sm font-medium">Live Rankings</span>
              </div>
            </div>

            {/* Next-Gen Tab System */}
            <div className="flex space-x-3 bg-black/20 rounded-2xl p-2 backdrop-blur-sm border border-white/10 mb-8">
              <button
                onClick={() => setActiveTab('free')}
                className={`flex-1 relative overflow-hidden rounded-xl py-4 px-6 font-bold text-lg transition-all duration-300 ${
                  activeTab === 'free'
                    ? 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg shadow-emerald-500/25 transform scale-[1.02]'
                    : 'text-white/70 hover:text-white hover:bg-white/5'
                }`}
              >
                {activeTab === 'free' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-teal-400 opacity-20 animate-pulse"></div>
                )}
                <div className="relative flex items-center justify-center gap-3">
                  <Sparkles className="w-6 h-6" />
                  <div className="text-center">
                    <div>Free Bestsellers</div>
                    <div className="text-sm opacity-80 font-normal">Zero cost, infinite value</div>
                  </div>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('paid')}
                className={`flex-1 relative overflow-hidden rounded-xl py-4 px-6 font-bold text-lg transition-all duration-300 ${
                  activeTab === 'paid'
                    ? 'bg-gradient-to-r from-violet-500 to-purple-500 text-white shadow-lg shadow-violet-500/25 transform scale-[1.02]'
                    : 'text-white/70 hover:text-white hover:bg-white/5'
                }`}
              >
                {activeTab === 'paid' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-400 to-purple-400 opacity-20 animate-pulse"></div>
                )}
                <div className="relative flex items-center justify-center gap-3">
                  <Crown className="w-6 h-6" />
                  <div className="text-center">
                    <div>Premium Bestsellers</div>
                    <div className="text-sm opacity-80 font-normal">Support creators directly</div>
                  </div>
                </div>
              </button>
            </div>

            {/* Futuristic Genre Filter */}
            {categories.length > 0 && (
              <div className="flex items-center gap-4">
                <label className="text-white font-semibold whitespace-nowrap flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Explore Genres:
                </label>
                <div className="relative flex-1">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-6 py-3 bg-black/30 border border-white/20 rounded-xl text-white font-medium focus:ring-2 focus:ring-purple-500 focus:border-purple-500 backdrop-blur-sm appearance-none cursor-pointer"
                  >
                    <option value="all" className="bg-gray-900">🌍 All Genres</option>
                    {categories.map((category) => (
                      <option key={category.genre} value={category.genre} className="bg-gray-900">
                        📚 {category.genre} ({category.book_count} books)
                      </option>
                    ))}
                  </select>
                  <ChevronRight className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50 pointer-events-none" />
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="p-8 bg-gradient-to-b from-slate-900/50 to-black/50">
        {loading ? (
          <div className="flex items-center justify-center py-16">
            <div className="flex flex-col items-center gap-6">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-500/20 border-t-purple-500"></div>
                <div className="absolute inset-0 animate-ping rounded-full h-16 w-16 border-4 border-purple-500/10"></div>
              </div>
              <p className="text-white font-bold text-lg">Discovering bestsellers...</p>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              </div>
            </div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="space-y-4">
            {bestsellers.map((book, index) => {
              const rankIcon = getRankIcon(book.rank)
              const rankBadge = getRankBadge(book.rank)
              const RankIconComponent = rankIcon.icon

              return (
                <Link
                  key={book.id}
                  href={`/books/${book.id}`}
                  className="block relative group"
                >
                  {/* Glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-orange-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative bg-gradient-to-r from-slate-800/80 via-slate-800/60 to-slate-800/80 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-purple-500/30 transition-all duration-300 overflow-hidden">
                    {/* Rank stripe */}
                    <div className={`absolute left-0 top-0 bottom-0 w-1 ${
                      book.rank === 1 ? 'bg-gradient-to-b from-yellow-400 to-yellow-600' :
                      book.rank === 2 ? 'bg-gradient-to-b from-gray-300 to-gray-500' :
                      book.rank === 3 ? 'bg-gradient-to-b from-amber-400 to-amber-600' :
                      'bg-gradient-to-b from-blue-400 to-blue-600'
                    }`}></div>

                    <div className="flex items-center gap-8 p-8">
                      {/* Epic Rank Section */}
                      <div className="flex-shrink-0 flex flex-col items-center gap-4">
                        <div className="relative">
                          <div className={`absolute inset-0 rounded-full blur-lg ${
                            book.rank === 1 ? 'bg-yellow-400/50' :
                            book.rank === 2 ? 'bg-gray-400/50' :
                            book.rank === 3 ? 'bg-amber-400/50' :
                            'bg-blue-400/50'
                          } animate-pulse`}></div>
                          <div className={`relative w-16 h-16 rounded-full border-3 flex items-center justify-center ${
                            book.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-600 border-yellow-300' :
                            book.rank === 2 ? 'bg-gradient-to-br from-gray-300 to-gray-500 border-gray-200' :
                            book.rank === 3 ? 'bg-gradient-to-br from-amber-400 to-amber-600 border-amber-300' :
                            'bg-gradient-to-br from-blue-400 to-blue-600 border-blue-300'
                          }`}>
                            <RankIconComponent className="w-8 h-8 text-white drop-shadow-lg" />
                          </div>
                        </div>
                        <div className={`px-4 py-2 rounded-xl text-sm font-black ${rankBadge.color} shadow-lg`}>
                          {rankBadge.text}
                        </div>
                      </div>

                      {/* Cinematic Book Cover */}
                      <div className="flex-shrink-0 relative">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                        <div className="relative w-24 h-36 bg-gradient-to-br from-slate-700 to-slate-900 rounded-2xl overflow-hidden shadow-2xl border border-white/10 group-hover:scale-105 transition-transform duration-300">
                          {book.cover_image_url ? (
                            <img
                              src={book.cover_image_url}
                              alt={book.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-purple-600 via-pink-600 to-orange-600 flex items-center justify-center">
                              <BookOpen className="w-10 h-10 text-white drop-shadow-lg" />
                            </div>
                          )}
                          {/* Holographic overlay */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                      </div>

                      {/* Premium Book Details */}
                      <div className="flex-1 min-w-0 space-y-4">
                        <div>
                          <h3 className="text-2xl font-black text-white group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-pink-400 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 line-clamp-2 leading-tight">
                            {book.title}
                          </h3>
                          <p className="text-purple-200 font-semibold text-lg">
                            by {book.author_name}
                          </p>
                        </div>

                        {book.genre && (
                          <div className="flex items-center gap-2">
                            <span className="px-3 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-200 text-sm rounded-full border border-purple-500/30 backdrop-blur-sm">
                              {book.genre}
                            </span>
                          </div>
                        )}

                        <div className="flex items-center gap-6 text-white/80">
                          <div className="flex items-center gap-2">
                            <div className="p-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg">
                              <Download className="w-5 h-5 text-green-400" />
                            </div>
                            <div>
                              <span className="font-black text-xl text-white">{book.sales_count.toLocaleString()}</span>
                              <span className="text-sm ml-1">downloads</span>
                            </div>
                          </div>
                          {book.is_daily_data && (
                            <div className="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full border border-orange-500/30">
                              <Fire className="w-4 h-4 text-orange-400" />
                              <span className="text-sm font-bold text-orange-200">Live Chart</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Epic Price & CTA */}
                      <div className="flex-shrink-0 text-right space-y-4">
                        <div className="text-4xl font-black text-white">
                          {formatPrice(book.price_amount)}
                        </div>
                        <div className="relative group/button">
                          <div className={`absolute inset-0 rounded-xl blur-lg ${
                            book.price_amount && book.price_amount > 0
                              ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                              : 'bg-gradient-to-r from-green-500 to-emerald-500'
                          } opacity-50 group-hover/button:opacity-75 transition-opacity`}></div>
                          <button className={`relative px-6 py-3 rounded-xl font-black text-lg text-white transition-all duration-300 transform group-hover/button:scale-105 ${
                            book.price_amount && book.price_amount > 0
                              ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-400 hover:to-purple-400'
                              : 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400'
                          }`}>
                            {book.price_amount && book.price_amount > 0 ? '💎 Buy Now' : '⚡ Get Free'}
                          </button>
                        </div>
                        <div className="text-sm text-white/60 font-medium">
                          Global Rank #{book.rank}
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
                  </div>
                </Link>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-xl border-2 border-dashed border-gray-200">
            <div className="max-w-md mx-auto">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <BookOpen className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">
                No {activeTab} bestsellers yet
              </h3>
              <p className="text-gray-600 mb-6">
                {selectedCategory === 'all'
                  ? `Be the first to publish a ${activeTab} book and claim the #1 spot!`
                  : `No ${activeTab} books found in ${selectedCategory}. Try a different genre or be the first to publish in this category!`
                }
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <TrendingUp className="w-4 h-4" />
                <span>Rankings update daily at 8AM</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
