'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Star, TrendingUp, BookOpen, DollarSign } from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
}

interface Category {
  genre: string
  book_count: number
}

interface EnhancedBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function EnhancedBestsellerChart({ limit = 10, showHeader = true }: EnhancedBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })

      if (error) {
        console.error('Bestsellers query error:', error)
        throw error
      }

      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })

      if (error) {
        console.error('Categories query error:', error)
        return
      }

      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return '🥇'
    if (rank === 2) return '🥈'
    if (rank === 3) return '🥉'
    return `#${rank}`
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      {showHeader && (
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="w-6 h-6 text-amber-600" />
            <h2 className="text-xl font-serif text-gray-800">Bestsellers</h2>
          </div>
          
          {/* Free/Paid Tabs */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-4">
            <button
              onClick={() => setActiveTab('free')}
              className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                activeTab === 'free'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <BookOpen className="w-4 h-4" />
              Free Books
            </button>
            <button
              onClick={() => setActiveTab('paid')}
              className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                activeTab === 'paid'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <DollarSign className="w-4 h-4" />
              Paid Books
            </button>
          </div>

          {/* Category Filter */}
          {categories.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-sm"
              >
                <option value="all">All Categories</option>
                {categories.map((category) => (
                  <option key={category.genre} value={category.genre}>
                    {category.genre} ({category.book_count})
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="space-y-4">
            {bestsellers.map((book) => (
              <Link
                key={book.id}
                href={`/books/${book.id}`}
                className="flex items-center gap-4 p-4 rounded-lg border border-gray-100 hover:border-amber-200 hover:bg-amber-50 transition-all group"
              >
                {/* Rank */}
                <div className="flex-shrink-0 w-12 text-center">
                  <span className="text-lg font-bold text-amber-600">
                    {getRankIcon(book.rank)}
                  </span>
                </div>

                {/* Cover */}
                <div className="flex-shrink-0 w-12 h-16 bg-gray-100 rounded overflow-hidden">
                  {book.cover_image_url ? (
                    <img
                      src={book.cover_image_url}
                      alt={book.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center">
                      <BookOpen className="w-6 h-6 text-amber-600" />
                    </div>
                  )}
                </div>

                {/* Book Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-800 group-hover:text-amber-700 transition-colors truncate">
                    {book.title}
                  </h3>
                  <p className="text-sm text-gray-600 truncate">
                    by {book.author_name}
                  </p>
                  {book.genre && (
                    <p className="text-xs text-gray-500 mt-1">
                      {book.genre}
                    </p>
                  )}
                </div>

                {/* Stats */}
                <div className="flex-shrink-0 text-right">
                  <div className="text-sm font-medium text-gray-800">
                    {formatPrice(book.price_amount)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {book.sales_count} downloads
                  </div>
                  {book.is_daily_data && (
                    <div className="text-xs text-amber-600 mt-1">
                      Daily Chart
                    </div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">
              No {activeTab} bestsellers yet
            </h3>
            <p className="text-gray-500">
              {selectedCategory === 'all' 
                ? `No ${activeTab} books with sales found.`
                : `No ${activeTab} books in ${selectedCategory} category.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
