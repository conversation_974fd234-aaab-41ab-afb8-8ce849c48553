'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { TrendingUp, BookOpen, DollarSign, Crown, Award, Medal, Trophy, Download, Flame } from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
  average_rating?: number
  review_count?: number
}

interface Category {
  genre: string
  book_count: number
}

interface CleanBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function CleanBestsellerChart({ limit = 10, showHeader = true }: CleanBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })
      if (error) throw error
      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })
      if (error) return
      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return Crown
    if (rank === 2) return Award
    if (rank === 3) return Medal
    return Trophy
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { text: '#1 Best Seller', color: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white' }
    if (rank === 2) return { text: '#2 Best Seller', color: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white' }
    if (rank === 3) return { text: '#3 Best Seller', color: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white' }
    if (rank <= 10) return { text: `#${rank} Best Seller`, color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' }
    return { text: `#${rank}`, color: 'bg-gray-100 text-gray-600' }
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      {showHeader && (
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-amber-400 to-orange-500 rounded-full"></div>
            <div>
              <h2 className="text-2xl font-serif text-gray-900 mb-1">Bestsellers</h2>
              <p className="text-gray-600 text-sm">Most downloaded books by independent authors</p>
            </div>
          </div>

          <div className="flex space-x-1 bg-gray-50 rounded-xl p-1 mb-6">
            <button
              onClick={() => setActiveTab('free')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-sm transition-all ${
                activeTab === 'free'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Free Books
            </button>
            <button
              onClick={() => setActiveTab('paid')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-sm transition-all ${
                activeTab === 'paid'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Premium Books
            </button>
          </div>

          {categories.length > 0 && (
            <div className="flex items-center gap-3">
              <label className="text-gray-700 font-medium text-sm whitespace-nowrap">Genre:</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                <option value="all">All Genres</option>
                {categories.map((category) => (
                  <option key={category.genre} value={category.genre}>
                    {category.genre} ({category.book_count})
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-amber-200 border-t-amber-600"></div>
              <span className="text-gray-600 font-medium">Loading bestsellers...</span>
            </div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {bestsellers.map((book) => (
              <Link
                key={book.id}
                href={`/books/${book.id}`}
                className="group block bg-white border border-gray-100 rounded-lg hover:shadow-md hover:border-amber-200 transition-all duration-200"
              >
                <div className="p-2">
                  {/* Rank Badge */}
                  <div className="flex items-center justify-between mb-2">
                    <div className={`text-xs font-medium ${
                      book.rank === 1 ? 'text-amber-600' :
                      book.rank <= 3 ? 'text-gray-700' :
                      'text-gray-500'
                    }`}>
                      #{book.rank}
                    </div>
                    <div className="text-sm font-bold text-gray-900">
                      {formatPrice(book.price_amount)}
                    </div>
                  </div>

                  {/* Book Cover - Compact KDP */}
                  <div className="w-full aspect-[2/3] bg-gray-100 rounded overflow-hidden mb-2">
                    {book.cover_image_url ? (
                      <img
                        src={book.cover_image_url}
                        alt={book.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-amber-50 to-orange-50 flex items-center justify-center">
                        <BookOpen className="w-6 h-6 text-amber-400" />
                      </div>
                    )}
                  </div>

                  {/* Book Info - Very Compact */}
                  <div className="space-y-1">
                    <h3 className="font-medium text-gray-900 text-xs line-clamp-2 leading-tight">
                      {book.title}
                    </h3>
                    <p className="text-gray-600 text-xs">by {book.author_name}</p>

                    {/* Pen Rating */}
                    {(book.average_rating && book.average_rating > 0) ? (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <span className="text-amber-500">🖊️</span>
                        <span>{book.average_rating.toFixed(1)}</span>
                        {book.review_count && book.review_count > 0 && (
                          <span className="text-gray-400">({book.review_count})</span>
                        )}
                      </div>
                    ) : (
                      <div className="text-xs text-gray-400">No reviews</div>
                    )}

                    <div className="text-xs text-gray-500">
                      {book.sales_count.toLocaleString()} downloads
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No {activeTab} bestsellers yet</h3>
            <p className="text-gray-600 text-sm">Be the first to publish and claim the #1 spot!</p>
          </div>
        )}
      </div>
    </div>
  )
}
