'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { TrendingUp, BookOpen, DollarSign, Crown, Award, Medal, Trophy, Download, Flame } from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
}

interface Category {
  genre: string
  book_count: number
}

interface CleanBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function CleanBestsellerChart({ limit = 10, showHeader = true }: CleanBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })
      if (error) throw error
      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })
      if (error) return
      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return Crown
    if (rank === 2) return Award
    if (rank === 3) return Medal
    return Trophy
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { text: '#1 Best Seller', color: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white' }
    if (rank === 2) return { text: '#2 Best Seller', color: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white' }
    if (rank === 3) return { text: '#3 Best Seller', color: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white' }
    if (rank <= 10) return { text: `#${rank} Best Seller`, color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' }
    return { text: `#${rank}`, color: 'bg-gray-100 text-gray-600' }
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      {showHeader && (
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-amber-400 to-orange-500 rounded-full"></div>
            <div>
              <h2 className="text-2xl font-serif text-gray-900 mb-1">Bestsellers</h2>
              <p className="text-gray-600 text-sm">Most downloaded books by independent authors</p>
            </div>
          </div>

          <div className="flex space-x-1 bg-gray-50 rounded-xl p-1 mb-6">
            <button
              onClick={() => setActiveTab('free')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-sm transition-all ${
                activeTab === 'free'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Free Books
            </button>
            <button
              onClick={() => setActiveTab('paid')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-sm transition-all ${
                activeTab === 'paid'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Premium Books
            </button>
          </div>

          {categories.length > 0 && (
            <div className="flex items-center gap-3">
              <label className="text-gray-700 font-medium text-sm whitespace-nowrap">Genre:</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                <option value="all">All Genres</option>
                {categories.map((category) => (
                  <option key={category.genre} value={category.genre}>
                    {category.genre} ({category.book_count})
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-amber-200 border-t-amber-600"></div>
              <span className="text-gray-600 font-medium">Loading bestsellers...</span>
            </div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="space-y-3">
            {bestsellers.map((book) => {
              const RankIcon = getRankIcon(book.rank)
              const rankBadge = getRankBadge(book.rank)

              return (
                <Link
                  key={book.id}
                  href={`/books/${book.id}`}
                  className="block bg-white border border-gray-100 rounded-xl hover:border-amber-200 hover:shadow-md transition-all p-4 group"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex flex-col items-center gap-2">
                      <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                        <span className="text-amber-600 font-bold text-sm">#{book.rank}</span>
                      </div>
                      {book.rank <= 3 && (
                        <div className="text-xs text-amber-600 font-medium">
                          {book.rank === 1 ? '🥇' : book.rank === 2 ? '🥈' : '🥉'}
                        </div>
                      )}
                    </div>

                    <div className="w-12 h-16 bg-gray-100 rounded-lg overflow-hidden shadow-sm">
                      {book.cover_image_url ? (
                        <img src={book.cover_image_url} alt={book.title} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center">
                          <BookOpen className="w-6 h-6 text-amber-600" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 group-hover:text-amber-700 transition-colors line-clamp-2 leading-tight">
                        {book.title}
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">by {book.author_name}</p>
                      {book.genre && (
                        <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full mt-2">
                          {book.genre}
                        </span>
                      )}
                      <div className="flex items-center gap-1 text-gray-500 text-sm mt-2">
                        <Download className="w-4 h-4" />
                        <span>{book.sales_count.toLocaleString()} downloads</span>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900 mb-2">{formatPrice(book.price_amount)}</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        book.price_amount && book.price_amount > 0
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-green-100 text-green-700'
                      }`}>
                        {book.price_amount && book.price_amount > 0 ? 'Premium' : 'Free'}
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No {activeTab} bestsellers yet</h3>
            <p className="text-gray-600 text-sm">Be the first to publish and claim the #1 spot!</p>
          </div>
        )}
      </div>
    </div>
  )
}
