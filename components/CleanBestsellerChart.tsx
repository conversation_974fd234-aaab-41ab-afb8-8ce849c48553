'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { TrendingUp, BookOpen, DollarSign, Crown, Award, Medal, Trophy, Download, Flame } from 'lucide-react'

interface BestsellerBook {
  id: string
  title: string
  author_name: string
  author_id: string
  cover_image_url?: string
  price_amount?: number
  genre?: string
  sales_count: number
  rank: number
  is_daily_data: boolean
}

interface Category {
  genre: string
  book_count: number
}

interface CleanBestsellerChartProps {
  limit?: number
  showHeader?: boolean
}

export function CleanBestsellerChart({ limit = 10, showHeader = true }: CleanBestsellerChartProps) {
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('free')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [bestsellers, setBestsellers] = useState<BestsellerBook[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
    fetchCategories()
  }, [activeTab, selectedCategory, limit])

  const fetchBestsellers = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase.rpc('get_bestsellers_by_category', {
        book_type_param: activeTab,
        category_param: selectedCategory === 'all' ? null : selectedCategory,
        limit_param: limit
      })
      if (error) throw error
      setBestsellers(data || [])
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
      setBestsellers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase.rpc('get_bestseller_categories', {
        book_type_param: activeTab
      })
      if (error) return
      setCategories(data || [])
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const formatPrice = (priceAmount?: number) => {
    if (!priceAmount || priceAmount === 0) return 'Free'
    return `$${(priceAmount / 100).toFixed(2)}`
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return Crown
    if (rank === 2) return Award
    if (rank === 3) return Medal
    return Trophy
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return { text: '#1 Best Seller', color: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white' }
    if (rank === 2) return { text: '#2 Best Seller', color: 'bg-gradient-to-r from-gray-400 to-gray-600 text-white' }
    if (rank === 3) return { text: '#3 Best Seller', color: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white' }
    if (rank <= 10) return { text: `#${rank} Best Seller`, color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' }
    return { text: `#${rank}`, color: 'bg-gray-100 text-gray-600' }
  }

  return (
    <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl shadow-2xl border border-purple-500/20 overflow-hidden">
      {showHeader && (
        <div className="bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-orange-600/10 p-8 border-b border-purple-500/20">
          <div className="flex items-center gap-4 mb-8">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
              <Flame className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-black bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                Global Bestsellers
              </h1>
              <p className="text-purple-200 text-lg font-medium">
                The world's most downloaded independent books
              </p>
            </div>
          </div>

          <div className="flex space-x-3 bg-black/20 rounded-2xl p-2 mb-8">
            <button
              onClick={() => setActiveTab('free')}
              className={`flex-1 py-4 px-6 rounded-xl font-bold text-lg transition-all ${
                activeTab === 'free'
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white'
                  : 'text-white/70 hover:text-white'
              }`}
            >
              Free Books
            </button>
            <button
              onClick={() => setActiveTab('paid')}
              className={`flex-1 py-4 px-6 rounded-xl font-bold text-lg transition-all ${
                activeTab === 'paid'
                  ? 'bg-gradient-to-r from-violet-500 to-purple-500 text-white'
                  : 'text-white/70 hover:text-white'
              }`}
            >
              Premium Books
            </button>
          </div>

          {categories.length > 0 && (
            <div className="flex items-center gap-4">
              <label className="text-white font-semibold">Genre:</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="flex-1 px-4 py-2 bg-black/30 border border-white/20 rounded-xl text-white"
              >
                <option value="all">All Genres</option>
                {categories.map((category) => (
                  <option key={category.genre} value={category.genre}>
                    {category.genre} ({category.book_count})
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      <div className="p-8">
        {loading ? (
          <div className="flex items-center justify-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-500/20 border-t-purple-500"></div>
          </div>
        ) : bestsellers.length > 0 ? (
          <div className="space-y-4">
            {bestsellers.map((book) => {
              const RankIcon = getRankIcon(book.rank)
              const rankBadge = getRankBadge(book.rank)
              
              return (
                <Link
                  key={book.id}
                  href={`/books/${book.id}`}
                  className="block bg-gradient-to-r from-slate-800/80 to-slate-800/60 rounded-2xl border border-white/10 hover:border-purple-500/30 transition-all p-6"
                >
                  <div className="flex items-center gap-6">
                    <div className="flex flex-col items-center gap-2">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <RankIcon className="w-6 h-6 text-white" />
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-bold ${rankBadge.color}`}>
                        {rankBadge.text}
                      </div>
                    </div>

                    <div className="w-20 h-28 bg-gradient-to-br from-slate-700 to-slate-900 rounded-xl overflow-hidden">
                      {book.cover_image_url ? (
                        <img src={book.cover_image_url} alt={book.title} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                          <BookOpen className="w-8 h-8 text-white" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 space-y-2">
                      <h3 className="text-xl font-bold text-white">{book.title}</h3>
                      <p className="text-purple-200">by {book.author_name}</p>
                      {book.genre && (
                        <span className="inline-block px-2 py-1 bg-purple-500/20 text-purple-200 text-sm rounded-full">
                          {book.genre}
                        </span>
                      )}
                      <div className="flex items-center gap-2 text-white/80">
                        <Download className="w-4 h-4" />
                        <span>{book.sales_count.toLocaleString()} downloads</span>
                      </div>
                    </div>

                    <div className="text-right space-y-2">
                      <div className="text-2xl font-bold text-white">{formatPrice(book.price_amount)}</div>
                      <button className={`px-4 py-2 rounded-lg font-bold text-white ${
                        book.price_amount && book.price_amount > 0
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                          : 'bg-gradient-to-r from-green-500 to-emerald-500'
                      }`}>
                        {book.price_amount && book.price_amount > 0 ? 'Buy Now' : 'Get Free'}
                      </button>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <BookOpen className="w-16 h-16 text-white/30 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">No {activeTab} bestsellers yet</h3>
            <p className="text-white/60">Be the first to publish and claim the #1 spot!</p>
          </div>
        )}
      </div>
    </div>
  )
}
