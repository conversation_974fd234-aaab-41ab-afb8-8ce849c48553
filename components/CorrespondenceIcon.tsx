'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface CorrespondenceIconProps {
  userId: string
  onClick: () => void
}

export function CorrespondenceIcon({ userId, onClick }: CorrespondenceIconProps) {
  const [unreadCount, setUnreadCount] = useState(0)
  const [hasNewCorrespondence, setHasNewCorrespondence] = useState(false)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (userId) {
      fetchUnreadCount()
      
      // Set up real-time subscription for new notifications
      const subscription = supabase
        .channel('correspondence')
        .on('postgres_changes', 
          { 
            event: 'INSERT', 
            schema: 'public', 
            table: 'notifications',
            filter: `user_id=eq.${userId}`
          }, 
          () => {
            setHasNewCorrespondence(true)
            fetchUnreadCount()
            
            // Reset the animation after a moment
            setTimeout(() => setHasNewCorrespondence(false), 2000)
          }
        )
        .subscribe()

      return () => {
        subscription.unsubscribe()
      }
    }
  }, [userId])

  const fetchUnreadCount = async () => {
    try {
      const { count } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .is('read_at', null)

      setUnreadCount(count || 0)
    } catch (error) {
      console.error('Error fetching unread count:', error)
    }
  }

  return (
    <button
      onClick={onClick}
      className="relative p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 group"
      title="Correspondence"
    >
      {/* Quill Icon */}
      <div className={`relative transition-all duration-300 ${
        hasNewCorrespondence ? 'animate-pulse' : ''
      }`}>
        <svg 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          className={`transition-all duration-200 ${
            unreadCount > 0 
              ? 'text-amber-600' 
              : 'text-gray-600 group-hover:text-gray-800'
          }`}
        >
          {/* Quill Feather */}
          <path 
            d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="1.5" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
          {/* Quill Tip */}
          <path 
            d="M16 8L2 22" 
            stroke="currentColor" 
            strokeWidth="1.5" 
            strokeLinecap="round"
          />
          {/* Ink Drop */}
          <path 
            d="M17.5 15H9" 
            stroke="currentColor" 
            strokeWidth="1.5" 
            strokeLinecap="round"
          />
        </svg>

        {/* Gentle Glow for Unread */}
        {unreadCount > 0 && (
          <div className="absolute inset-0 bg-amber-400 rounded-full opacity-20 animate-pulse"></div>
        )}

        {/* Ink Drop Animation for New Correspondence */}
        {hasNewCorrespondence && (
          <div className="absolute -top-1 -right-1">
            <div className="w-3 h-3 bg-amber-500 rounded-full animate-ping"></div>
            <div className="absolute top-0 right-0 w-3 h-3 bg-amber-600 rounded-full"></div>
          </div>
        )}
      </div>

      {/* Unread Count Badge */}
      {unreadCount > 0 && (
        <div className="absolute -top-1 -right-1 min-w-[20px] h-5 bg-amber-500 text-white text-xs font-medium rounded-full flex items-center justify-center px-1.5 shadow-sm">
          {unreadCount > 99 ? '99+' : unreadCount}
        </div>
      )}

      {/* Subtle Writing Animation on Hover */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className="absolute bottom-1 right-1 w-1 h-1 bg-amber-600 rounded-full animate-pulse"></div>
      </div>
    </button>
  )
}
