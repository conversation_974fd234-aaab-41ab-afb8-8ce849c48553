"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, Search, Highlighter, MessageCircle, X, Palette } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigation } from "@/contexts/NavigationContext"

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
}

interface Highlight {
  id: string
  start_position: number
  end_position: number
  selected_text: string
  color: string
  is_private: boolean
  user_id: string
  created_at: string
}

interface MarginComment {
  id: string
  content: string
  position_data: {
    startPos: number
    endPos: number
    anchorText: string
  }
  user_id: string
  parent_id?: string
  created_at: string
  replies?: MarginComment[]
}

interface EbookReaderProps {
  chapters: Chapter[]
  bookTitle: string
  authorName: string
  projectId: string
  userId?: string
  onClose: () => void
  isPreview?: boolean
  onApprovePreview?: () => void
}

export function EbookReader({ chapters, bookTitle, authorName, projectId, userId, onClose, isPreview = false, onApprovePreview }: EbookReaderProps) {
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()

  // Core reader state
  const [currentChapter, setCurrentChapter] = useState(0)
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('serif')
  const [lineHeight, setLineHeight] = useState(1.6)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [showChapterList, setShowChapterList] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [bookmarks, setBookmarks] = useState<number[]>([])

  // Social features state
  const [highlights, setHighlights] = useState<Highlight[]>([])
  const [marginComments, setMarginComments] = useState<MarginComment[]>([])
  const [selectedText, setSelectedText] = useState<{
    text: string
    startPos: number
    endPos: number
  } | null>(null)
  const [showHighlightMenu, setShowHighlightMenu] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [highlightColor, setHighlightColor] = useState('#ffeb3b')
  const [newComment, setNewComment] = useState('')
  const [isPageTurning, setIsPageTurning] = useState(false)

  // Refs for text selection and page animation
  const contentRef = useRef<HTMLDivElement>(null)
  const pageRef = useRef<HTMLDivElement>(null)

  // Hide navigation when reader opens, show when it closes
  useEffect(() => {
    hideNavigation()

    // Show navigation when component unmounts (reader closes)
    return () => {
      showNavigation()
    }
  }, [hideNavigation, showNavigation])

  // Wrapper function to ensure navigation is shown when closing
  const handleClose = useCallback(() => {
    showNavigation()
    onClose()
  }, [showNavigation, onClose])

  // Calculate reading progress
  useEffect(() => {
    const totalChapters = chapters.length
    const progress = totalChapters > 0 ? ((currentChapter + 1) / totalChapters) * 100 : 0
    setReadingProgress(progress)
  }, [currentChapter, chapters.length])

  // Load highlights and comments for current chapter
  useEffect(() => {
    if (!userId || !chapters[currentChapter]) return

    const loadChapterData = async () => {
      const chapterId = chapters[currentChapter].id

      // Load highlights
      const { data: highlightsData } = await supabase
        .from('highlights')
        .select('*')
        .eq('chapter_id', chapterId)
        .eq('project_id', projectId)

      // Load margin comments
      const { data: commentsData } = await supabase
        .from('margin_comments')
        .select('*')
        .eq('chapter_id', chapterId)
        .eq('project_id', projectId)
        .order('created_at', { ascending: true })

      setHighlights(highlightsData || [])
      setMarginComments(commentsData || [])
    }

    loadChapterData()
  }, [currentChapter, projectId, userId, supabase, chapters])

  // Enhanced page turning with animation and auto-scroll to top
  const nextChapter = useCallback(() => {
    if (currentChapter < chapters.length - 1 && !isPageTurning) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentChapter(currentChapter + 1)
        setIsPageTurning(false)
        // Scroll to top of the new chapter
        if (pageRef.current) {
          pageRef.current.scrollTo({ top: 0, behavior: 'smooth' })
        }
      }, 300)
    }
  }, [currentChapter, chapters.length, isPageTurning])

  const prevChapter = useCallback(() => {
    if (currentChapter > 0 && !isPageTurning) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentChapter(currentChapter - 1)
        setIsPageTurning(false)
        // Scroll to top of the new chapter
        if (pageRef.current) {
          pageRef.current.scrollTo({ top: 0, behavior: 'smooth' })
        }
      }, 300)
    }
  }, [currentChapter, isPageTurning])

  // Text selection handling
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)
    const selectedText = selection.toString().trim()

    if (selectedText.length > 0 && contentRef.current?.contains(range.commonAncestorContainer)) {
      const startPos = range.startOffset
      const endPos = range.endOffset

      setSelectedText({
        text: selectedText,
        startPos,
        endPos
      })
      setShowHighlightMenu(true)
    }
  }, [])

  // Create highlight
  const createHighlight = async (color: string, isPrivate: boolean = false) => {
    if (!selectedText || !userId || !chapters[currentChapter]) return

    const chapterId = chapters[currentChapter].id

    const { data, error } = await supabase
      .from('highlights')
      .insert({
        user_id: userId,
        project_id: projectId,
        chapter_id: chapterId,
        start_position: selectedText.startPos,
        end_position: selectedText.endPos,
        selected_text: selectedText.text,
        color,
        is_private: isPrivate
      })
      .select()
      .single()

    if (!error && data) {
      setHighlights(prev => [...prev, data])

      // Update passage popularity
      await supabase.rpc('increment_passage_popularity', {
        p_project_id: projectId,
        p_chapter_id: chapterId,
        p_passage_text: selectedText.text
      })
    }

    setSelectedText(null)
    setShowHighlightMenu(false)
    window.getSelection()?.removeAllRanges()
  }

  // Create margin comment
  const createMarginComment = async () => {
    if (!selectedText || !newComment.trim() || !userId || !chapters[currentChapter]) return

    const chapterId = chapters[currentChapter].id

    const { data, error } = await supabase
      .from('margin_comments')
      .insert({
        user_id: userId,
        project_id: projectId,
        chapter_id: chapterId,
        position_data: {
          startPos: selectedText.startPos,
          endPos: selectedText.endPos,
          anchorText: selectedText.text
        },
        content: newComment.trim()
      })
      .select()
      .single()

    if (!error && data) {
      setMarginComments(prev => [...prev, data])
    }

    setNewComment('')
    setShowCommentForm(false)
    setSelectedText(null)
    window.getSelection()?.removeAllRanges()
  }

  const toggleBookmark = () => {
    if (bookmarks.includes(currentChapter)) {
      setBookmarks(bookmarks.filter(b => b !== currentChapter))
    } else {
      setBookmarks([...bookmarks, currentChapter])
    }
  }

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100'
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900'
      default:
        return 'bg-white text-gray-900'
    }
  }

  // Simple markdown-like renderer for preserved formatting
  const renderFormattedText = (text: string) => {
    return text
      .split('\n')
      .map((line, index) => {
        // Handle headings
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-2xl font-bold mb-4 mt-6">{line.substring(2)}</h1>
        }
        if (line.startsWith('## ')) {
          return <h2 key={index} className="text-xl font-bold mb-3 mt-5">{line.substring(3)}</h2>
        }
        if (line.startsWith('### ')) {
          return <h3 key={index} className="text-lg font-bold mb-2 mt-4">{line.substring(4)}</h3>
        }
        if (line.startsWith('#### ')) {
          return <h4 key={index} className="text-base font-bold mb-2 mt-3">{line.substring(5)}</h4>
        }
        if (line.startsWith('##### ')) {
          return <h5 key={index} className="text-sm font-bold mb-1 mt-2">{line.substring(6)}</h5>
        }
        if (line.startsWith('###### ')) {
          return <h6 key={index} className="text-xs font-bold mb-1 mt-2">{line.substring(7)}</h6>
        }

        // Handle blockquotes
        if (line.startsWith('> ')) {
          return <blockquote key={index} className="border-l-4 border-gray-300 pl-4 italic my-2">{line.substring(2)}</blockquote>
        }

        // Handle list items
        if (line.startsWith('• ')) {
          return <li key={index} className="ml-4 mb-1">{line.substring(2)}</li>
        }

        // Handle empty lines
        if (line.trim() === '') {
          return <br key={index} />
        }

        // Handle regular paragraphs with inline formatting
        const formattedLine = line
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')

        return (
          <p
            key={index}
            className="mb-3 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: formattedLine }}
          />
        )
      })
  }

  // Render highlights and formatting in text
  const renderTextWithHighlights = (text: string) => {
    // First apply formatting
    let result = text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')

    // Then apply highlights
    if (highlights.length > 0) {
      highlights.forEach(highlight => {
        const highlightedText = `<mark style="background-color: ${highlight.color}; padding: 2px 4px; border-radius: 3px;" data-highlight-id="${highlight.id}">${highlight.selected_text}</mark>`
        result = result.replace(highlight.selected_text, highlightedText)
      })
    }

    return result
  }

  const currentChapterData = chapters[currentChapter]

  if (!currentChapterData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <div className="text-red-500 text-4xl mb-4">📚</div>
            <h3 className="text-lg font-semibold mb-2">No Chapters Available</h3>
            <p className="text-gray-600 mb-4">This book doesn't have any readable chapters yet.</p>
            <Button onClick={handleClose}>Close Reader</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`fixed inset-0 z-50 ${getThemeClasses()}`}>
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        {/* Mobile Header - Stacked Layout */}
        <div className="block sm:hidden">
          <div className="flex items-center justify-between p-3">
            <div>
              <h1
                className="font-semibold text-base"
                style={{
                  maxWidth: '180px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70 truncate">by {authorName}</p>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.href = '/library'}
                title="Go to Library"
              >
                📚
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleBookmark}
                className={bookmarks.includes(currentChapter) ? 'text-yellow-500' : ''}
                title="Bookmark Chapter"
              >
                <Bookmark className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHighlightMenu(!showHighlightMenu)}
                className="relative"
                title="View Highlights"
              >
                <Highlighter className="h-4 w-4" />
                {highlights.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {highlights.length}
                  </span>
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCommentForm(!showCommentForm)}
                className="relative"
                title="Add Comment"
              >
                <MessageCircle className="h-4 w-4" />
                {marginComments.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {marginComments.length}
                  </span>
                )}
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setShowChapterList(!showChapterList)} title="Chapter List">
                <BookOpen className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Header - Horizontal Layout */}
        <div className="hidden sm:flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <div>
              <h1
                className="font-semibold text-lg"
                style={{
                  maxWidth: '300px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70">by {authorName}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/library'}
              title="Go to Library"
            >
              📚
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleBookmark}
              className={bookmarks.includes(currentChapter) ? 'text-yellow-500' : ''}
              title="Bookmark Chapter"
            >
              <Bookmark className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHighlightMenu(!showHighlightMenu)}
              className="relative"
              title="View Highlights"
            >
              <Highlighter className="h-4 w-4" />
              {highlights.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {highlights.length}
                </span>
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCommentForm(!showCommentForm)}
              className="relative"
              title="Add Comment"
            >
              <MessageCircle className="h-4 w-4" />
              {marginComments.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {marginComments.length}
                </span>
              )}
            </Button>

            <Button variant="ghost" size="sm" onClick={() => setShowChapterList(!showChapterList)} title="Chapter List">
              <BookOpen className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Progress Bar */}
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
        <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded-full">
          <div
            className="h-full bg-blue-600 transition-all duration-300 rounded-full"
            style={{ width: `${readingProgress}%` }}
          />
        </div>
        <div className="text-xs opacity-70 mt-1 text-center">
          {Math.round(readingProgress)}% complete • {highlights.length} highlights • {marginComments.length} comments
        </div>
      </div>

      <div className="flex h-full">
        {/* Chapter List Sidebar */}
        {showChapterList && (
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Chapters</h3>
              <div className="space-y-2">
                {chapters.map((chapter, index) => (
                  <button
                    key={chapter.id}
                    onClick={() => {
                      setCurrentChapter(index)
                      setShowChapterList(false)
                      // Scroll to top when selecting chapter from list
                      setTimeout(() => {
                        if (pageRef.current) {
                          pageRef.current.scrollTo({ top: 0, behavior: 'smooth' })
                        }
                      }, 100)
                    }}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      index === currentChapter 
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100' 
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    <div className="font-medium text-sm">
                      Chapter {chapter.chapter_number}
                    </div>
                    <div className="text-xs opacity-70 mt-1">
                      {chapter.title}
                    </div>
                    <div className="text-xs opacity-50 mt-1">
                      {chapter.word_count.toLocaleString()} words
                    </div>
                    {bookmarks.includes(index) && (
                      <div className="text-yellow-500 text-xs mt-1">
                        🔖 Bookmarked
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Settings Sidebar */}
        {showSettings && (
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Reading Settings</h3>
              
              <div className="space-y-6">
                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Size</label>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    >
                      A-
                    </Button>
                    <span className="text-sm">{fontSize}px</span>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    >
                      A+
                    </Button>
                  </div>
                </div>

                {/* Font Family */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Family</label>
                  <select 
                    value={fontFamily}
                    onChange={(e) => setFontFamily(e.target.value)}
                    className="w-full p-2 border rounded-md bg-background"
                  >
                    <option value="serif">Serif</option>
                    <option value="sans-serif">Sans Serif</option>
                    <option value="monospace">Monospace</option>
                  </select>
                </div>

                {/* Line Height */}
                <div>
                  <label className="block text-sm font-medium mb-2">Line Height</label>
                  <input
                    type="range"
                    min="1.2"
                    max="2.0"
                    step="0.1"
                    value={lineHeight}
                    onChange={(e) => setLineHeight(parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <span className="text-sm">{lineHeight}</span>
                </div>

                {/* Theme */}
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={theme === 'light' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('light')}
                    >
                      Light
                    </Button>
                    <Button
                      variant={theme === 'dark' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('dark')}
                    >
                      Dark
                    </Button>
                    <Button
                      variant={theme === 'sepia' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('sepia')}
                    >
                      Sepia
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Reading Area with Page Turn Animation */}
        <div className="flex-1 flex flex-col relative">
          <motion.div
            className="flex-1 overflow-y-auto"
            initial={false}
            animate={{
              rotateY: isPageTurning ? -15 : 0,
              scale: isPageTurning ? 0.95 : 1
            }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            ref={pageRef}
          >
            <div className="max-w-4xl mx-auto p-8 relative">
              <div className="mb-8">
                <h2
                  className="text-2xl font-bold mb-2"
                  style={{
                    fontSize: `${fontSize + 8}px`,
                    fontFamily,
                    lineHeight
                  }}
                >
                  {currentChapterData.title}
                </h2>
                <div className="text-sm opacity-70">
                  Chapter {currentChapterData.chapter_number} • {currentChapterData.word_count.toLocaleString()} words
                </div>
              </div>

              <div
                ref={contentRef}
                className="prose prose-lg max-w-none relative"
                style={{
                  fontSize: `${fontSize}px`,
                  fontFamily,
                  lineHeight
                }}
                onMouseUp={handleTextSelection}
                onTouchEnd={handleTextSelection}
              >
                {currentChapterData.content.split('\n').map((line, index) => {
                  // Handle headings
                  if (line.startsWith('# ')) {
                    return <h1 key={index} className="text-2xl font-bold mb-4 mt-6 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(2)) }} />
                  }
                  if (line.startsWith('## ')) {
                    return <h2 key={index} className="text-xl font-bold mb-3 mt-5 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(3)) }} />
                  }
                  if (line.startsWith('### ')) {
                    return <h3 key={index} className="text-lg font-bold mb-2 mt-4 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(4)) }} />
                  }
                  if (line.startsWith('#### ')) {
                    return <h4 key={index} className="text-base font-bold mb-2 mt-3 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(5)) }} />
                  }
                  if (line.startsWith('##### ')) {
                    return <h5 key={index} className="text-sm font-bold mb-1 mt-2 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(6)) }} />
                  }
                  if (line.startsWith('###### ')) {
                    return <h6 key={index} className="text-xs font-bold mb-1 mt-2 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(7)) }} />
                  }

                  // Handle blockquotes
                  if (line.startsWith('> ')) {
                    return <blockquote key={index} className="border-l-4 border-gray-300 pl-4 italic my-2 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(2)) }} />
                  }

                  // Handle list items
                  if (line.startsWith('• ')) {
                    return <li key={index} className="ml-4 mb-1 relative" dangerouslySetInnerHTML={{ __html: renderTextWithHighlights(line.substring(2)) }} />
                  }

                  // Handle empty lines
                  if (line.trim() === '') {
                    return <br key={index} />
                  }

                  // Handle regular paragraphs
                  return (
                    <p
                      key={index}
                      className="mb-4 relative leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: renderTextWithHighlights(line)
                      }}
                    />
                  )
                })}
              </div>

              {/* Margin Comments Display */}
              {marginComments.map((comment, index) => (
                <div
                  key={comment.id}
                  className="absolute right-0 w-64 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg shadow-sm"
                  style={{
                    top: `${200 + index * 120}px`,
                    transform: 'translateX(calc(100% + 16px))'
                  }}
                >
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    "{comment.position_data.anchorText.substring(0, 30)}..."
                  </div>
                  <div className="text-sm">{comment.content}</div>
                  <div className="text-xs opacity-70 mt-2">
                    {new Date(comment.created_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Navigation Footer */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              <Button 
                variant="outline"
                onClick={prevChapter}
                disabled={currentChapter === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              
              <div className="text-sm opacity-70">
                {currentChapter + 1} of {chapters.length}
              </div>
              
              <Button 
                variant="outline"
                onClick={nextChapter}
                disabled={currentChapter === chapters.length - 1}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Highlight Menu */}
      <AnimatePresence>
        {showHighlightMenu && selectedText && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <Card className="w-full max-w-md mx-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Highlight Text</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowHighlightMenu(false)
                      setSelectedText(null)
                      window.getSelection()?.removeAllRanges()
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-sm italic">"{selectedText.text}"</p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">Highlight Color</label>
                  <div className="flex space-x-2">
                    {['#ffeb3b', '#4caf50', '#2196f3', '#ff9800', '#e91e63'].map(color => (
                      <button
                        key={color}
                        className={`w-8 h-8 rounded-full border-2 ${
                          highlightColor === color ? 'border-gray-800 dark:border-white' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setHighlightColor(color)}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={() => createHighlight(highlightColor, false)}
                    className="flex-1"
                  >
                    <Highlighter className="h-4 w-4 mr-2" />
                    Public Highlight
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => createHighlight(highlightColor, true)}
                    className="flex-1"
                  >
                    Private
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  onClick={() => setShowCommentForm(true)}
                  className="w-full mt-2"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Add Comment Instead
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Comment Form */}
      <AnimatePresence>
        {showCommentForm && selectedText && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <Card className="w-full max-w-md mx-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Add Comment</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowCommentForm(false)
                      setSelectedText(null)
                      setNewComment('')
                      window.getSelection()?.removeAllRanges()
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-sm italic">"{selectedText.text}"</p>
                </div>

                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Share your thoughts about this passage..."
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none h-24 bg-white dark:bg-gray-800"
                  maxLength={500}
                />

                <div className="text-xs text-gray-500 mb-4">
                  {newComment.length}/500 characters
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={createMarginComment}
                    disabled={!newComment.trim()}
                    className="flex-1"
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Add Comment
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowHighlightMenu(true)}
                    className="flex-1"
                  >
                    Highlight Instead
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating Approve Button for Preview Mode */}
      {isPreview && onApprovePreview && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={onApprovePreview}
            className="bg-green-600 hover:bg-green-700 text-white shadow-lg px-6 py-3 text-base font-semibold rounded-full"
            size="lg"
          >
            ✓ Approve & Publish Book
          </Button>
        </div>
      )}
    </div>
  )
}

export default EbookReader
