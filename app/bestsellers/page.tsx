"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { CleanBestsellerChart } from "@/components/CleanBestsellerChart"

export default function BestsellersPage() {

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center">
            <Link
              href="/books"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              ← Back to All Books
            </Link>
          </div>
        </div>
      </div>

      {/* Bestsellers Chart */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CleanBestsellerChart
          showHeader={true}
          limit={50}
        />
        
        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              📚 Discover More Books
            </h3>
            <p className="text-gray-600 mb-6">
              Explore our full collection of books across all genres and discover your next favorite read.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/books">
                <Button className="bg-purple-600 text-white hover:bg-purple-700">
                  Browse All Books
                </Button>
              </Link>
              <Link href="/write/upload-ebook">
                <Button variant="outline">
                  📖 Publish Your Book
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
