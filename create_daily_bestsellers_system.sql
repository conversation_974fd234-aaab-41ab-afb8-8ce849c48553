-- Create daily bestsellers tracking system
-- This will update every day at 8AM with the previous day's sales

-- Create daily bestsellers table
CREATE TABLE IF NOT EXISTS daily_bestsellers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    book_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    book_type VARCHAR(10) NOT NULL CHECK (book_type IN ('free', 'paid')),
    rank INTEGER NOT NULL,
    sales_count INTEGER NOT NULL DEFAULT 0,
    title VARCHAR(255) NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    cover_image_url TEXT,
    price_amount INTEGER, -- in cents, null for free books
    genre VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date, book_type, rank)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_daily_bestsellers_date ON daily_bestsellers(date);
CREATE INDEX IF NOT EXISTS idx_daily_bestsellers_type ON daily_bestsellers(book_type);
CREATE INDEX IF NOT EXISTS idx_daily_bestsellers_rank ON daily_bestsellers(rank);
CREATE INDEX IF NOT EXISTS idx_daily_bestsellers_book_id ON daily_bestsellers(book_id);

-- Enable RLS
ALTER TABLE daily_bestsellers ENABLE ROW LEVEL SECURITY;

-- RLS Policy - anyone can view bestsellers
DROP POLICY IF EXISTS "Anyone can view daily bestsellers" ON daily_bestsellers;
CREATE POLICY "Anyone can view daily bestsellers" ON daily_bestsellers
    FOR SELECT USING (true);

-- Function to calculate and update daily bestsellers
CREATE OR REPLACE FUNCTION update_daily_bestsellers()
RETURNS void AS $$
DECLARE
    target_date DATE := CURRENT_DATE - INTERVAL '1 day'; -- Previous day
    free_book RECORD;
    paid_book RECORD;
    current_rank INTEGER;
BEGIN
    -- Delete existing data for the target date
    DELETE FROM daily_bestsellers WHERE date = target_date;
    
    -- Calculate FREE bestsellers (price_amount IS NULL or = 0)
    current_rank := 1;
    FOR free_book IN
        SELECT 
            p.id,
            p.title,
            p.cover_image_url,
            p.price_amount,
            p.genre,
            p.sales_count,
            u.id as author_id,
            u.name as author_name
        FROM projects p
        JOIN users u ON p.user_id = u.id
        WHERE p.is_ebook = true 
        AND p.is_complete = true
        AND (p.price_amount IS NULL OR p.price_amount = 0)
        AND p.sales_count > 0
        ORDER BY p.sales_count DESC, p.created_at DESC
        LIMIT 20
    LOOP
        INSERT INTO daily_bestsellers (
            date, book_id, book_type, rank, sales_count,
            title, author_name, author_id, cover_image_url, 
            price_amount, genre
        ) VALUES (
            target_date, free_book.id, 'free', current_rank, free_book.sales_count,
            free_book.title, free_book.author_name, free_book.author_id, 
            free_book.cover_image_url, free_book.price_amount, free_book.genre
        );
        current_rank := current_rank + 1;
    END LOOP;
    
    -- Calculate PAID bestsellers (price_amount > 0)
    current_rank := 1;
    FOR paid_book IN
        SELECT 
            p.id,
            p.title,
            p.cover_image_url,
            p.price_amount,
            p.genre,
            p.sales_count,
            u.id as author_id,
            u.name as author_name
        FROM projects p
        JOIN users u ON p.user_id = u.id
        WHERE p.is_ebook = true 
        AND p.is_complete = true
        AND p.price_amount > 0
        AND p.sales_count > 0
        ORDER BY p.sales_count DESC, p.created_at DESC
        LIMIT 20
    LOOP
        INSERT INTO daily_bestsellers (
            date, book_id, book_type, rank, sales_count,
            title, author_name, author_id, cover_image_url, 
            price_amount, genre
        ) VALUES (
            target_date, paid_book.id, 'paid', current_rank, paid_book.sales_count,
            paid_book.title, paid_book.author_name, paid_book.author_id, 
            paid_book.cover_image_url, paid_book.price_amount, paid_book.genre
        );
        current_rank := current_rank + 1;
    END LOOP;
    
    RAISE NOTICE 'Daily bestsellers updated for date: %', target_date;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get current bestsellers (falls back to live data if no daily data)
CREATE OR REPLACE FUNCTION get_bestsellers(book_type_param VARCHAR(10), limit_param INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    author_name VARCHAR(255),
    author_id UUID,
    cover_image_url TEXT,
    price_amount INTEGER,
    genre VARCHAR(100),
    sales_count INTEGER,
    rank INTEGER,
    is_daily_data BOOLEAN
) AS $$
DECLARE
    latest_date DATE;
BEGIN
    -- Get the latest date with data
    SELECT MAX(date) INTO latest_date 
    FROM daily_bestsellers 
    WHERE book_type = book_type_param;
    
    -- If we have daily data, return it
    IF latest_date IS NOT NULL AND latest_date >= CURRENT_DATE - INTERVAL '2 days' THEN
        RETURN QUERY
        SELECT 
            db.book_id as id,
            db.title,
            db.author_name,
            db.author_id,
            db.cover_image_url,
            db.price_amount,
            db.genre,
            db.sales_count,
            db.rank,
            true as is_daily_data
        FROM daily_bestsellers db
        WHERE db.date = latest_date 
        AND db.book_type = book_type_param
        ORDER BY db.rank
        LIMIT limit_param;
    ELSE
        -- Fall back to live data
        IF book_type_param = 'free' THEN
            RETURN QUERY
            SELECT 
                p.id,
                p.title,
                u.name as author_name,
                u.id as author_id,
                p.cover_image_url,
                p.price_amount,
                p.genre,
                p.sales_count,
                ROW_NUMBER() OVER (ORDER BY p.sales_count DESC, p.created_at DESC)::INTEGER as rank,
                false as is_daily_data
            FROM projects p
            JOIN users u ON p.user_id = u.id
            WHERE p.is_ebook = true 
            AND p.is_complete = true
            AND (p.price_amount IS NULL OR p.price_amount = 0)
            AND p.sales_count > 0
            ORDER BY p.sales_count DESC, p.created_at DESC
            LIMIT limit_param;
        ELSE
            RETURN QUERY
            SELECT 
                p.id,
                p.title,
                u.name as author_name,
                u.id as author_id,
                p.cover_image_url,
                p.price_amount,
                p.genre,
                p.sales_count,
                ROW_NUMBER() OVER (ORDER BY p.sales_count DESC, p.created_at DESC)::INTEGER as rank,
                false as is_daily_data
            FROM projects p
            JOIN users u ON p.user_id = u.id
            WHERE p.is_ebook = true 
            AND p.is_complete = true
            AND p.price_amount > 0
            AND p.sales_count > 0
            ORDER BY p.sales_count DESC, p.created_at DESC
            LIMIT limit_param;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Initialize with current data
SELECT update_daily_bestsellers();
