-- Add category-specific bestseller support
-- This extends the daily bestsellers system to support genre/category rankings

-- Function to get bestsellers by category
CREATE OR REPLACE FUNCTION get_bestsellers_by_category(
    book_type_param VARCHAR(10), 
    category_param TEXT DEFAULT NULL,
    limit_param INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    author_name TEXT,
    author_id UUID,
    cover_image_url TEXT,
    price_amount INTEGER,
    genre TEXT,
    sales_count INTEGER,
    rank INTEGER,
    is_daily_data BOOLEAN
) AS $$
DECLARE
    latest_date DATE;
BEGIN
    -- Get the latest date with data
    SELECT MAX(date) INTO latest_date 
    FROM daily_bestsellers 
    WHERE book_type = book_type_param
    AND (category_param IS NULL OR genre = category_param);
    
    -- If we have daily data, return it
    IF latest_date IS NOT NULL AND latest_date >= CURRENT_DATE - INTERVAL '2 days' THEN
        RETURN QUERY
        SELECT 
            db.project_id as id,
            db.title,
            db.author_name,
            db.author_id,
            db.cover_image_url,
            db.price_amount,
            db.genre,
            db.sales_count,
            ROW_NUMBER() OVER (ORDER BY db.sales_count DESC, db.title)::INTEGER as rank,
            true as is_daily_data
        FROM daily_bestsellers db
        WHERE db.date = latest_date 
        AND db.book_type = book_type_param
        AND (category_param IS NULL OR db.genre = category_param)
        ORDER BY db.sales_count DESC, db.title
        LIMIT limit_param;
    ELSE
        -- Fall back to live data
        IF book_type_param = 'free' THEN
            RETURN QUERY
            SELECT 
                p.id,
                p.title,
                COALESCE(p.author_name, u.name) as author_name,
                p.user_id as author_id,
                p.cover_image_url,
                p.price_amount,
                p.genre,
                p.sales_count,
                ROW_NUMBER() OVER (ORDER BY p.sales_count DESC, p.created_at DESC)::INTEGER as rank,
                false as is_daily_data
            FROM projects p
            JOIN users u ON p.user_id = u.id
            WHERE p.is_ebook = true 
            AND p.is_complete = true
            AND (p.price_amount IS NULL OR p.price_amount = 0)
            AND p.sales_count > 0
            AND (category_param IS NULL OR p.genre = category_param)
            ORDER BY p.sales_count DESC, p.created_at DESC
            LIMIT limit_param;
        ELSE
            RETURN QUERY
            SELECT 
                p.id,
                p.title,
                COALESCE(p.author_name, u.name) as author_name,
                p.user_id as author_id,
                p.cover_image_url,
                p.price_amount,
                p.genre,
                p.sales_count,
                ROW_NUMBER() OVER (ORDER BY p.sales_count DESC, p.created_at DESC)::INTEGER as rank,
                false as is_daily_data
            FROM projects p
            JOIN users u ON p.user_id = u.id
            WHERE p.is_ebook = true 
            AND p.is_complete = true
            AND p.price_amount > 0
            AND p.sales_count > 0
            AND (category_param IS NULL OR p.genre = category_param)
            ORDER BY p.sales_count DESC, p.created_at DESC
            LIMIT limit_param;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get available genres/categories
CREATE OR REPLACE FUNCTION get_bestseller_categories(book_type_param VARCHAR(10))
RETURNS TABLE (
    genre TEXT,
    book_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.genre,
        COUNT(*)::INTEGER as book_count
    FROM projects p
    WHERE p.is_ebook = true 
    AND p.is_complete = true
    AND p.sales_count > 0
    AND p.genre IS NOT NULL
    AND p.genre != ''
    AND (
        (book_type_param = 'free' AND (p.price_amount IS NULL OR p.price_amount = 0))
        OR 
        (book_type_param = 'paid' AND p.price_amount > 0)
    )
    GROUP BY p.genre
    HAVING COUNT(*) > 0
    ORDER BY COUNT(*) DESC, p.genre;
END;
$$ LANGUAGE plpgsql;
